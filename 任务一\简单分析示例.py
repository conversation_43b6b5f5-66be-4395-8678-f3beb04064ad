#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MovieLens数据集分析示例 - 简化版
演示SQL查询的基本逻辑
"""

import csv
from collections import defaultdict

def load_movies():
    """加载电影数据"""
    movies = {}
    with open('movielen数据集/movies.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            movies[int(row['movieId'])] = {
                'title': row['title'],
                'genres': row['genres']
            }
    return movies

def load_ratings():
    """加载评分数据"""
    ratings = []
    with open('movielen数据集/ratings.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            ratings.append({
                'userId': int(row['userId']),
                'movieId': int(row['movieId']),
                'rating': float(row['rating'])
            })
    return ratings

def task1_demo():
    """任务1演示：平均得分前10的电影"""
    print("========================================")
    print("任务1：平均得分前10的电影")
    print("========================================")
    
    movies = load_movies()
    ratings = load_ratings()
    
    # 计算每部电影的平均评分
    movie_ratings = defaultdict(list)
    for rating in ratings:
        movie_ratings[rating['movieId']].append(rating['rating'])
    
    # 计算平均分和评分数量
    movie_stats = []
    for movie_id, rating_list in movie_ratings.items():
        if len(rating_list) >= 10:  # 至少10个评分
            avg_rating = sum(rating_list) / len(rating_list)
            movie_stats.append({
                'movieId': movie_id,
                'title': movies[movie_id]['title'],
                'avg_rating': avg_rating,
                'rating_count': len(rating_list)
            })
    
    # 按平均评分排序
    movie_stats.sort(key=lambda x: (-x['avg_rating'], -x['rating_count']))
    
    print("平均得分前10的电影（至少10个评分）：")
    for i, movie in enumerate(movie_stats[:10], 1):
        print(f"{i:2d}. {movie['title']:<50} 评分: {movie['avg_rating']:.2f} ({movie['rating_count']}个)")

def task2_demo():
    """任务2演示：Action类型电影的平均得分前10"""
    print("\n========================================")
    print("任务2：Action类型电影的平均得分前10")
    print("========================================")
    
    movies = load_movies()
    ratings = load_ratings()
    
    # 找出Action类型的电影
    action_movies = {}
    for movie_id, movie_info in movies.items():
        if 'Action' in movie_info['genres']:
            action_movies[movie_id] = movie_info
    
    # 计算Action电影的平均评分
    movie_ratings = defaultdict(list)
    for rating in ratings:
        if rating['movieId'] in action_movies:
            movie_ratings[rating['movieId']].append(rating['rating'])
    
    # 计算平均分
    action_stats = []
    for movie_id, rating_list in movie_ratings.items():
        if len(rating_list) >= 5:  # 至少5个评分
            avg_rating = sum(rating_list) / len(rating_list)
            action_stats.append({
                'movieId': movie_id,
                'title': action_movies[movie_id]['title'],
                'avg_rating': avg_rating,
                'rating_count': len(rating_list)
            })
    
    # 按平均评分排序
    action_stats.sort(key=lambda x: (-x['avg_rating'], -x['rating_count']))
    
    print("Action类型电影平均得分前10：")
    for i, movie in enumerate(action_stats[:10], 1):
        print(f"{i:2d}. {movie['title']:<45} 评分: {movie['avg_rating']:.2f} ({movie['rating_count']}个)")

def task3_demo():
    """任务3演示：用户1最喜欢的电影类型前5"""
    print("\n========================================")
    print("任务3：用户1最喜欢的电影类型前5")
    print("========================================")
    
    movies = load_movies()
    ratings = load_ratings()
    
    # 获取用户1的评分
    user1_ratings = [r for r in ratings if r['userId'] == 1]
    
    # 按类型统计评分
    genre_ratings = defaultdict(list)
    for rating in user1_ratings:
        movie_id = rating['movieId']
        if movie_id in movies:
            genres = movies[movie_id]['genres'].split('|')
            for genre in genres:
                genre = genre.strip()
                if genre and genre != '(no genres listed)':
                    genre_ratings[genre].append(rating['rating'])
    
    # 计算每个类型的平均评分
    genre_stats = []
    for genre, rating_list in genre_ratings.items():
        if len(rating_list) >= 3:  # 至少3个评分
            avg_rating = sum(rating_list) / len(rating_list)
            genre_stats.append({
                'genre': genre,
                'avg_rating': avg_rating,
                'movie_count': len(rating_list)
            })
    
    # 按平均评分排序
    genre_stats.sort(key=lambda x: (-x['avg_rating'], -x['movie_count']))
    
    print("用户1最喜欢的电影类型前5：")
    for i, genre in enumerate(genre_stats[:5], 1):
        print(f"{i}. {genre['genre']:<15} 平均评分: {genre['avg_rating']:.2f} ({genre['movie_count']}部电影)")

def task4_demo():
    """任务4演示：用户1观影次数最多的电影类型前5"""
    print("\n========================================")
    print("任务4：用户1观影次数最多的电影类型前5")
    print("========================================")
    
    movies = load_movies()
    ratings = load_ratings()
    
    # 获取用户1的评分
    user1_ratings = [r for r in ratings if r['userId'] == 1]
    
    # 按类型统计观影次数
    genre_counts = defaultdict(list)
    for rating in user1_ratings:
        movie_id = rating['movieId']
        if movie_id in movies:
            genres = movies[movie_id]['genres'].split('|')
            for genre in genres:
                genre = genre.strip()
                if genre and genre != '(no genres listed)':
                    genre_counts[genre].append(rating['rating'])
    
    # 计算每个类型的观影次数和平均评分
    genre_stats = []
    for genre, rating_list in genre_counts.items():
        avg_rating = sum(rating_list) / len(rating_list)
        genre_stats.append({
            'genre': genre,
            'watch_count': len(rating_list),
            'avg_rating': avg_rating
        })
    
    # 按观影次数排序
    genre_stats.sort(key=lambda x: (-x['watch_count'], -x['avg_rating']))
    
    print("用户1观影次数最多的电影类型前5：")
    for i, genre in enumerate(genre_stats[:5], 1):
        print(f"{i}. {genre['genre']:<15} 观影次数: {genre['watch_count']:3d} (平均评分: {genre['avg_rating']:.2f})")

def main():
    """主函数"""
    try:
        print("MovieLens数据集分析演示")
        print("=" * 50)
        
        task1_demo()
        task2_demo()
        task3_demo()
        task4_demo()
        
        print("\n分析完成！")
        print("\n注意：这是简化版演示，完整的SQL查询请参考:")
        print("- movielen数据集分析.sql")
        print("- movielen数据集分析_简化版.sql")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
