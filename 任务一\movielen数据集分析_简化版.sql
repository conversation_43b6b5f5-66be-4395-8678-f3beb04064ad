-- MovieLens数据集分析查询 - 简化版
-- 适用于大多数SQL数据库

-- ========================================
-- 1. 列出平均得分前10的电影
-- ========================================

SELECT 
    m.movieId,
    m.title,
    ROUND(AVG(r.rating), 2) as avg_rating,
    COUNT(r.rating) as rating_count
FROM movies m
JOIN ratings r ON m.movieId = r.movieId
GROUP BY m.movieId, m.title
HAVING COUNT(r.rating) >= 10  -- 至少有10个评分才有统计意义
ORDER BY avg_rating DESC, rating_count DESC
LIMIT 10;

-- ========================================
-- 2. 列出每个类型的平均得分前10的电影 (简化版)
-- ========================================

-- 为了简化，我们分别查询主要的电影类型
-- Action类型电影的平均得分前10
SELECT 
    'Action' as genre,
    m.movieId,
    m.title,
    ROUND(AVG(r.rating), 2) as avg_rating,
    COUNT(r.rating) as rating_count
FROM movies m
JOIN ratings r ON m.movieId = r.movieId
WHERE m.genres LIKE '%Action%'
GROUP BY m.movieId, m.title
HAVING COUNT(r.rating) >= 5
ORDER BY avg_rating DESC, rating_count DESC
LIMIT 10;

-- Comedy类型电影的平均得分前10
SELECT 
    'Comedy' as genre,
    m.movieId,
    m.title,
    ROUND(AVG(r.rating), 2) as avg_rating,
    COUNT(r.rating) as rating_count
FROM movies m
JOIN ratings r ON m.movieId = r.movieId
WHERE m.genres LIKE '%Comedy%'
GROUP BY m.movieId, m.title
HAVING COUNT(r.rating) >= 5
ORDER BY avg_rating DESC, rating_count DESC
LIMIT 10;

-- Drama类型电影的平均得分前10
SELECT 
    'Drama' as genre,
    m.movieId,
    m.title,
    ROUND(AVG(r.rating), 2) as avg_rating,
    COUNT(r.rating) as rating_count
FROM movies m
JOIN ratings r ON m.movieId = r.movieId
WHERE m.genres LIKE '%Drama%'
GROUP BY m.movieId, m.title
HAVING COUNT(r.rating) >= 5
ORDER BY avg_rating DESC, rating_count DESC
LIMIT 10;

-- Thriller类型电影的平均得分前10
SELECT 
    'Thriller' as genre,
    m.movieId,
    m.title,
    ROUND(AVG(r.rating), 2) as avg_rating,
    COUNT(r.rating) as rating_count
FROM movies m
JOIN ratings r ON m.movieId = r.movieId
WHERE m.genres LIKE '%Thriller%'
GROUP BY m.movieId, m.title
HAVING COUNT(r.rating) >= 5
ORDER BY avg_rating DESC, rating_count DESC
LIMIT 10;

-- ========================================
-- 3. 列出每个用户综合评价排在前5的电影类型 (简化版)
-- ========================================

-- 用户对Action类型的综合评价
SELECT 
    r.userId,
    'Action' as genre,
    ROUND(AVG(r.rating), 2) as avg_rating,
    COUNT(r.rating) as rating_count
FROM ratings r
JOIN movies m ON r.movieId = m.movieId
WHERE m.genres LIKE '%Action%'
GROUP BY r.userId
HAVING COUNT(r.rating) >= 3
ORDER BY r.userId, avg_rating DESC;

-- 用户对Comedy类型的综合评价
SELECT 
    r.userId,
    'Comedy' as genre,
    ROUND(AVG(r.rating), 2) as avg_rating,
    COUNT(r.rating) as rating_count
FROM ratings r
JOIN movies m ON r.movieId = m.movieId
WHERE m.genres LIKE '%Comedy%'
GROUP BY r.userId
HAVING COUNT(r.rating) >= 3
ORDER BY r.userId, avg_rating DESC;

-- ========================================
-- 4. 列出每个用户观影次数排在前5的电影类型 (简化版)
-- ========================================

-- 用户观看Action类型电影的次数
SELECT
    r.userId,
    'Action' as genre,
    COUNT(r.rating) as watch_count,
    ROUND(AVG(r.rating), 2) as avg_rating
FROM ratings r
JOIN movies m ON r.movieId = m.movieId
WHERE m.genres LIKE '%Action%'
GROUP BY r.userId
ORDER BY r.userId, watch_count DESC;

-- 用户观看Comedy类型电影的次数
SELECT
    r.userId,
    'Comedy' as genre,
    COUNT(r.rating) as watch_count,
    ROUND(AVG(r.rating), 2) as avg_rating
FROM ratings r
JOIN movies m ON r.movieId = m.movieId
WHERE m.genres LIKE '%Comedy%'
GROUP BY r.userId
ORDER BY r.userId, watch_count DESC;

-- ========================================
-- 数据库建表语句 (可选)
-- ========================================

-- 创建movies表
CREATE TABLE IF NOT EXISTS movies (
    movieId INTEGER PRIMARY KEY,
    title VARCHAR(500),
    genres VARCHAR(200)
);

-- 创建ratings表
CREATE TABLE IF NOT EXISTS ratings (
    userId INTEGER,
    movieId INTEGER,
    rating DECIMAL(2,1),
    timestamp BIGINT,
    FOREIGN KEY (movieId) REFERENCES movies(movieId)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_ratings_movieId ON ratings(movieId);
CREATE INDEX IF NOT EXISTS idx_ratings_userId ON ratings(userId);
CREATE INDEX IF NOT EXISTS idx_movies_genres ON movies(genres);

-- ========================================
-- 数据导入示例 (MySQL/PostgreSQL)
-- ========================================

-- MySQL导入示例:
-- LOAD DATA INFILE 'movies.csv'
-- INTO TABLE movies
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

-- LOAD DATA INFILE 'ratings.csv'
-- INTO TABLE ratings
-- FIELDS TERMINATED BY ','
-- ENCLOSED BY '"'
-- LINES TERMINATED BY '\n'
-- IGNORE 1 ROWS;

-- PostgreSQL导入示例:
-- COPY movies FROM 'movies.csv' DELIMITER ',' CSV HEADER;
-- COPY ratings FROM 'ratings.csv' DELIMITER ',' CSV HEADER;
