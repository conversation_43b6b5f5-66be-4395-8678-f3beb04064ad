-- 任务二：SQL中的滑动窗口 - 股票金叉分析
-- 计算5日线上穿10日线（金叉）出现的位置

-- ========================================
-- 数据表结构说明
-- ========================================
-- Apple Dataset.csv: Date, Open, High, Low, Close, Adj Close, Volume
-- GOOGL.csv: Date, Open, High, Low, Close, Adj Close, Volume  
-- Tesla.csv: Date, Adj Close, Close, High, Low, Open, Volume

-- ========================================
-- 建表语句
-- ========================================

-- 创建Apple股票表
CREATE TABLE IF NOT EXISTS apple_stock (
    Date DATE,
    Open DECIMAL(10,6),
    High DECIMAL(10,6),
    Low DECIMAL(10,6),
    Close DECIMAL(10,6),
    Adj_Close DECIMAL(10,6),
    Volume BIGINT
);

-- 创建Google股票表
CREATE TABLE IF NOT EXISTS google_stock (
    Date DATE,
    Open DECIMAL(10,6),
    High DECIMAL(10,6),
    Low DECIMAL(10,6),
    Close DECIMAL(10,6),
    Adj_Close DECIMAL(10,6),
    Volume BIGINT
);

-- 创建Tesla股票表
CREATE TABLE IF NOT EXISTS tesla_stock (
    Date DATE,
    Adj_Close DECIMAL(10,6),
    Close DECIMAL(10,6),
    High DECIMAL(10,6),
    Low DECIMAL(10,6),
    Open DECIMAL(10,6),
    Volume BIGINT
);

-- ========================================
-- 1. Apple股票金叉分析
-- ========================================

WITH apple_ma AS (
    -- 计算5日和10日移动平均线
    SELECT 
        Date,
        Close,
        -- 5日移动平均线
        AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
        ) as MA5,
        -- 10日移动平均线
        AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
        ) as MA10,
        -- 前一日的5日移动平均线
        LAG(AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
        ), 1) OVER (ORDER BY Date) as prev_MA5,
        -- 前一日的10日移动平均线
        LAG(AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
        ), 1) OVER (ORDER BY Date) as prev_MA10,
        ROW_NUMBER() OVER (ORDER BY Date) as row_num
    FROM apple_stock
    ORDER BY Date
),
apple_golden_cross AS (
    -- 识别金叉点
    SELECT 
        Date,
        Close,
        MA5,
        MA10,
        prev_MA5,
        prev_MA10,
        CASE 
            WHEN row_num >= 10 -- 确保有足够数据计算10日均线
                AND prev_MA5 <= prev_MA10 -- 前一日5日线在10日线下方
                AND MA5 > MA10 -- 当日5日线在10日线上方
            THEN 'Golden Cross'
            ELSE NULL
        END as signal
    FROM apple_ma
    WHERE row_num >= 10
)
SELECT 
    Date,
    Close,
    ROUND(MA5, 4) as MA5,
    ROUND(MA10, 4) as MA10,
    signal as Golden_Cross_Signal
FROM apple_golden_cross
WHERE signal = 'Golden Cross'
ORDER BY Date;

-- ========================================
-- 2. Google股票金叉分析
-- ========================================

WITH google_ma AS (
    SELECT 
        Date,
        Close,
        AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
        ) as MA5,
        AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
        ) as MA10,
        LAG(AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
        ), 1) OVER (ORDER BY Date) as prev_MA5,
        LAG(AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
        ), 1) OVER (ORDER BY Date) as prev_MA10,
        ROW_NUMBER() OVER (ORDER BY Date) as row_num
    FROM google_stock
    ORDER BY Date
),
google_golden_cross AS (
    SELECT 
        Date,
        Close,
        MA5,
        MA10,
        prev_MA5,
        prev_MA10,
        CASE 
            WHEN row_num >= 10
                AND prev_MA5 <= prev_MA10
                AND MA5 > MA10
            THEN 'Golden Cross'
            ELSE NULL
        END as signal
    FROM google_ma
    WHERE row_num >= 10
)
SELECT 
    Date,
    Close,
    ROUND(MA5, 4) as MA5,
    ROUND(MA10, 4) as MA10,
    signal as Golden_Cross_Signal
FROM google_golden_cross
WHERE signal = 'Golden Cross'
ORDER BY Date;

-- ========================================
-- 3. Tesla股票金叉分析
-- ========================================

WITH tesla_ma AS (
    SELECT 
        Date,
        Close,
        AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
        ) as MA5,
        AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
        ) as MA10,
        LAG(AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
        ), 1) OVER (ORDER BY Date) as prev_MA5,
        LAG(AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
        ), 1) OVER (ORDER BY Date) as prev_MA10,
        ROW_NUMBER() OVER (ORDER BY Date) as row_num
    FROM tesla_stock
    ORDER BY Date
),
tesla_golden_cross AS (
    SELECT 
        Date,
        Close,
        MA5,
        MA10,
        prev_MA5,
        prev_MA10,
        CASE 
            WHEN row_num >= 10
                AND prev_MA5 <= prev_MA10
                AND MA5 > MA10
            THEN 'Golden Cross'
            ELSE NULL
        END as signal
    FROM tesla_ma
    WHERE row_num >= 10
)
SELECT
    Date,
    Close,
    ROUND(MA5, 4) as MA5,
    ROUND(MA10, 4) as MA10,
    signal as Golden_Cross_Signal
FROM tesla_golden_cross
WHERE signal = 'Golden Cross'
ORDER BY Date;

-- ========================================
-- 4. 综合分析：所有股票的金叉统计
-- ========================================

-- 统计每个股票的金叉次数
WITH all_golden_crosses AS (
    -- Apple金叉
    SELECT
        'AAPL' as stock_symbol,
        Date,
        Close,
        'Golden Cross' as signal_type
    FROM (
        WITH apple_ma AS (
            SELECT
                Date,
                Close,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW) as MA5,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW) as MA10,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA5,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA10,
                ROW_NUMBER() OVER (ORDER BY Date) as row_num
            FROM apple_stock
        )
        SELECT Date, Close
        FROM apple_ma
        WHERE row_num >= 10 AND prev_MA5 <= prev_MA10 AND MA5 > MA10
    ) apple_crosses

    UNION ALL

    -- Google金叉
    SELECT
        'GOOGL' as stock_symbol,
        Date,
        Close,
        'Golden Cross' as signal_type
    FROM (
        WITH google_ma AS (
            SELECT
                Date,
                Close,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW) as MA5,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW) as MA10,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA5,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA10,
                ROW_NUMBER() OVER (ORDER BY Date) as row_num
            FROM google_stock
        )
        SELECT Date, Close
        FROM google_ma
        WHERE row_num >= 10 AND prev_MA5 <= prev_MA10 AND MA5 > MA10
    ) google_crosses

    UNION ALL

    -- Tesla金叉
    SELECT
        'TSLA' as stock_symbol,
        Date,
        Close,
        'Golden Cross' as signal_type
    FROM (
        WITH tesla_ma AS (
            SELECT
                Date,
                Close,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW) as MA5,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW) as MA10,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA5,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA10,
                ROW_NUMBER() OVER (ORDER BY Date) as row_num
            FROM tesla_stock
        )
        SELECT Date, Close
        FROM tesla_ma
        WHERE row_num >= 10 AND prev_MA5 <= prev_MA10 AND MA5 > MA10
    ) tesla_crosses
)
SELECT
    stock_symbol,
    COUNT(*) as golden_cross_count,
    MIN(Date) as first_golden_cross,
    MAX(Date) as last_golden_cross,
    ROUND(AVG(Close), 2) as avg_price_at_golden_cross
FROM all_golden_crosses
GROUP BY stock_symbol
ORDER BY golden_cross_count DESC;

-- ========================================
-- 5. 按年份统计金叉分布
-- ========================================

WITH yearly_golden_crosses AS (
    SELECT
        'AAPL' as stock_symbol,
        YEAR(Date) as year,
        COUNT(*) as golden_cross_count
    FROM (
        WITH apple_ma AS (
            SELECT
                Date,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW) as MA5,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW) as MA10,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA5,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA10,
                ROW_NUMBER() OVER (ORDER BY Date) as row_num
            FROM apple_stock
        )
        SELECT Date
        FROM apple_ma
        WHERE row_num >= 10 AND prev_MA5 <= prev_MA10 AND MA5 > MA10
    ) apple_yearly
    GROUP BY YEAR(Date)

    UNION ALL

    SELECT
        'GOOGL' as stock_symbol,
        YEAR(Date) as year,
        COUNT(*) as golden_cross_count
    FROM (
        WITH google_ma AS (
            SELECT
                Date,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW) as MA5,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW) as MA10,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA5,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA10,
                ROW_NUMBER() OVER (ORDER BY Date) as row_num
            FROM google_stock
        )
        SELECT Date
        FROM google_ma
        WHERE row_num >= 10 AND prev_MA5 <= prev_MA10 AND MA5 > MA10
    ) google_yearly
    GROUP BY YEAR(Date)

    UNION ALL

    SELECT
        'TSLA' as stock_symbol,
        YEAR(Date) as year,
        COUNT(*) as golden_cross_count
    FROM (
        WITH tesla_ma AS (
            SELECT
                Date,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW) as MA5,
                AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW) as MA10,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA5,
                LAG(AVG(Close) OVER (ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW), 1) OVER (ORDER BY Date) as prev_MA10,
                ROW_NUMBER() OVER (ORDER BY Date) as row_num
            FROM tesla_stock
        )
        SELECT Date
        FROM tesla_ma
        WHERE row_num >= 10 AND prev_MA5 <= prev_MA10 AND MA5 > MA10
    ) tesla_yearly
    GROUP BY YEAR(Date)
)
SELECT
    year,
    SUM(CASE WHEN stock_symbol = 'AAPL' THEN golden_cross_count ELSE 0 END) as AAPL_golden_crosses,
    SUM(CASE WHEN stock_symbol = 'GOOGL' THEN golden_cross_count ELSE 0 END) as GOOGL_golden_crosses,
    SUM(CASE WHEN stock_symbol = 'TSLA' THEN golden_cross_count ELSE 0 END) as TSLA_golden_crosses,
    SUM(golden_cross_count) as total_golden_crosses
FROM yearly_golden_crosses
GROUP BY year
ORDER BY year;
