-- 滑动窗口核心示例 - 股票金叉检测
-- 展示SQL滑动窗口的核心概念和实现

-- ========================================
-- 核心概念演示
-- ========================================

-- 1. 基本滑动窗口 - 计算移动平均线
SELECT 
    Date,
    Close,
    -- 5日移动平均线（当前行及前4行）
    AVG(Close) OVER (
        ORDER BY Date 
        ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
    ) as MA5,
    
    -- 10日移动平均线（当前行及前9行）
    AVG(Close) OVER (
        ORDER BY Date 
        ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
    ) as MA10,
    
    -- 显示窗口大小（用于理解）
    COUNT(*) OVER (
        ORDER BY Date 
        ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
    ) as window_size_5,
    
    COUNT(*) OVER (
        ORDER BY Date 
        ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
    ) as window_size_10

FROM apple_stock 
ORDER BY Date
LIMIT 20;  -- 只显示前20行用于演示

-- ========================================
-- 2. 滑动窗口 + LAG函数 - 获取前一日数据
-- ========================================

SELECT 
    Date,
    Close,
    
    -- 当前移动平均线
    ROUND(AVG(Close) OVER (
        ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
    ), 4) as current_MA5,
    
    ROUND(AVG(Close) OVER (
        ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
    ), 4) as current_MA10,
    
    -- 前一日移动平均线
    ROUND(LAG(AVG(Close) OVER (
        ORDER BY Date ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
    ), 1) OVER (ORDER BY Date), 4) as prev_MA5,
    
    ROUND(LAG(AVG(Close) OVER (
        ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
    ), 1) OVER (ORDER BY Date), 4) as prev_MA10

FROM apple_stock 
ORDER BY Date
LIMIT 15;

-- ========================================
-- 3. 完整金叉检测逻辑
-- ========================================

WITH moving_averages AS (
    SELECT 
        Date,
        Close,
        -- 计算移动平均线
        AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
        ) as MA5,
        
        AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
        ) as MA10,
        
        -- 获取前一日移动平均线
        LAG(AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
        ), 1) OVER (ORDER BY Date) as prev_MA5,
        
        LAG(AVG(Close) OVER (
            ORDER BY Date 
            ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
        ), 1) OVER (ORDER BY Date) as prev_MA10,
        
        -- 行号（确保有足够数据）
        ROW_NUMBER() OVER (ORDER BY Date) as row_num
        
    FROM apple_stock
    ORDER BY Date
)
SELECT 
    Date,
    Close,
    ROUND(MA5, 4) as MA5,
    ROUND(MA10, 4) as MA10,
    ROUND(prev_MA5, 4) as prev_MA5,
    ROUND(prev_MA10, 4) as prev_MA10,
    
    -- 金叉检测
    CASE 
        WHEN row_num >= 10  -- 至少10天数据
            AND prev_MA5 <= prev_MA10  -- 前一日：5日线 ≤ 10日线
            AND MA5 > MA10  -- 当日：5日线 > 10日线
        THEN '🔥 金叉!'
        ELSE ''
    END as golden_cross_signal,
    
    -- 辅助信息
    CASE 
        WHEN row_num < 10 THEN '数据不足'
        WHEN MA5 > MA10 THEN '5日线在上'
        ELSE '10日线在上'
    END as ma_position

FROM moving_averages
WHERE row_num >= 8  -- 显示接近金叉的数据
ORDER BY Date;

-- ========================================
-- 4. 滑动窗口的其他应用示例
-- ========================================

-- 4.1 滑动最大值和最小值
SELECT 
    Date,
    Close,
    -- 5日内最高价
    MAX(Close) OVER (
        ORDER BY Date 
        ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
    ) as high_5d,
    
    -- 5日内最低价
    MIN(Close) OVER (
        ORDER BY Date 
        ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
    ) as low_5d,
    
    -- 5日价格波动范围
    MAX(Close) OVER (
        ORDER BY Date 
        ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
    ) - MIN(Close) OVER (
        ORDER BY Date 
        ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
    ) as range_5d

FROM apple_stock 
ORDER BY Date
LIMIT 10;

-- 4.2 滑动标准差（波动率）
SELECT 
    Date,
    Close,
    ROUND(AVG(Close) OVER (
        ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
    ), 4) as MA10,
    
    -- 10日标准差（如果数据库支持）
    -- ROUND(STDDEV(Close) OVER (
    --     ORDER BY Date ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
    -- ), 4) as volatility_10d

FROM apple_stock 
ORDER BY Date
LIMIT 10;

-- ========================================
-- 5. 窗口函数语法说明
-- ========================================

/*
滑动窗口语法结构：
FUNCTION() OVER (
    [PARTITION BY column]  -- 可选：分组
    ORDER BY column        -- 必需：排序
    ROWS BETWEEN start AND end  -- 窗口范围
)

窗口范围选项：
- CURRENT ROW: 当前行
- n PRECEDING: 前n行  
- n FOLLOWING: 后n行
- UNBOUNDED PRECEDING: 从第一行开始
- UNBOUNDED FOLLOWING: 到最后一行结束

常用组合：
- ROWS BETWEEN 4 PRECEDING AND CURRENT ROW  -- 5行窗口（包含当前行）
- ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING  -- 3行窗口（前1+当前+后1）
- ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW  -- 累计窗口

支持的函数：
- 聚合函数：SUM, AVG, COUNT, MAX, MIN, STDDEV
- 排名函数：ROW_NUMBER, RANK, DENSE_RANK
- 偏移函数：LAG, LEAD
- 分析函数：FIRST_VALUE, LAST_VALUE, NTH_VALUE
*/

-- ========================================
-- 6. 性能优化建议
-- ========================================

-- 创建索引提高性能
-- CREATE INDEX idx_apple_date ON apple_stock(Date);
-- CREATE INDEX idx_apple_date_close ON apple_stock(Date, Close);

-- 对于大数据集，考虑分区
-- CREATE TABLE apple_stock_partitioned (
--     Date DATE,
--     Close DECIMAL(10,4),
--     ...
-- ) PARTITION BY RANGE (YEAR(Date));

-- 使用物化视图缓存计算结果
-- CREATE MATERIALIZED VIEW apple_ma AS
-- SELECT Date, Close, MA5, MA10 FROM (滑动窗口查询);
