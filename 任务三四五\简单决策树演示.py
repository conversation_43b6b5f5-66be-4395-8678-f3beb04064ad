#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的决策树演示 - 世界幸福指数分析
演示SQL决策树的基本逻辑
"""

import csv

def load_and_analyze_data():
    """加载并分析数据"""
    print("世界幸福指数决策树分析")
    print("=" * 50)
    
    try:
        with open('世界幸福指数数据集/2019.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            data = list(reader)
        
        print(f"成功加载 {len(data)} 个国家的数据")
        
        # 显示前几行数据
        print(f"\n数据样本:")
        print(f"{'国家':<20} {'分数':<6} {'GDP':<6} {'社会支持':<8} {'健康':<6}")
        print("-" * 50)
        
        for i, row in enumerate(data[:10]):
            print(f"{row['Country or region']:<20} {row['Score']:<6} "
                  f"{row['GDP per capita']:<6} {row['Social support']:<8} "
                  f"{row['Healthy life expectancy']:<6}")
        
        return data
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return []

def classify_happiness(score):
    """分类幸福指数"""
    score = float(score)
    if score < 4.5:
        return 'Low'
    elif score < 6.5:
        return 'Middle'
    else:
        return 'High'

def apply_decision_tree(row):
    """应用决策树规则"""
    gdp = float(row['GDP per capita'])
    social = float(row['Social support'])
    health = float(row['Healthy life expectancy'])
    freedom = float(row['Freedom to make life choices'])
    
    # 决策树规则（基于数据分析）
    if gdp >= 1.2:
        # 高GDP分支
        if social >= 1.4:
            return 'High'
        elif social >= 1.0:
            return 'Middle'
        else:
            return 'Middle'
    elif gdp >= 0.8:
        # 中等GDP分支
        if health >= 0.8:
            return 'Middle'
        elif social >= 1.2:
            return 'Middle'
        else:
            return 'Low'
    else:
        # 低GDP分支
        if social >= 1.0 and freedom >= 0.4:
            return 'Middle'
        elif social >= 0.8:
            return 'Low'
        else:
            return 'Low'

def analyze_decision_tree():
    """分析决策树性能"""
    data = load_and_analyze_data()
    if not data:
        return
    
    print(f"\n决策树分类分析:")
    print("=" * 80)
    
    # 统计分类结果
    correct = 0
    total = len(data)
    
    # 混淆矩阵
    confusion = {
        'High': {'High': 0, 'Middle': 0, 'Low': 0},
        'Middle': {'High': 0, 'Middle': 0, 'Low': 0},
        'Low': {'High': 0, 'Middle': 0, 'Low': 0}
    }
    
    # 等级统计
    level_stats = {'High': 0, 'Middle': 0, 'Low': 0}
    
    print(f"{'国家':<20} {'实际':<8} {'预测':<8} {'正确':<6} {'分数':<6} {'GDP':<6}")
    print("-" * 70)
    
    for i, row in enumerate(data):
        actual = classify_happiness(row['Score'])
        predicted = apply_decision_tree(row)
        is_correct = actual == predicted
        
        if is_correct:
            correct += 1
        
        confusion[actual][predicted] += 1
        level_stats[actual] += 1
        
        # 显示前20个结果
        if i < 20:
            print(f"{row['Country or region']:<20} {actual:<8} {predicted:<8} "
                  f"{'✓' if is_correct else '✗':<6} {row['Score']:<6} {row['GDP per capita']:<6}")
    
    accuracy = correct / total * 100
    
    print(f"\n分类统计:")
    print(f"总样本数: {total}")
    print(f"正确分类: {correct}")
    print(f"准确率: {accuracy:.2f}%")
    
    print(f"\n各等级分布:")
    for level, count in level_stats.items():
        percentage = count / total * 100
        print(f"{level}: {count} 个国家 ({percentage:.1f}%)")
    
    print(f"\n混淆矩阵:")
    print(f"{'实际\\预测':<12} {'High':<8} {'Middle':<8} {'Low':<8}")
    print("-" * 40)
    
    for actual in ['High', 'Middle', 'Low']:
        print(f"{actual:<12} ", end="")
        for predicted in ['High', 'Middle', 'Low']:
            print(f"{confusion[actual][predicted]:<8}", end="")
        print()
    
    # 计算各类别的精确率和召回率
    print(f"\n性能指标:")
    for level in ['High', 'Middle', 'Low']:
        # 精确率 = TP / (TP + FP)
        tp = confusion[level][level]
        fp = sum(confusion[other][level] for other in ['High', 'Middle', 'Low'] if other != level)
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        
        # 召回率 = TP / (TP + FN)
        fn = sum(confusion[level][other] for other in ['High', 'Middle', 'Low'] if other != level)
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        
        # F1分数
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        print(f"{level}级别 - 精确率: {precision:.3f}, 召回率: {recall:.3f}, F1: {f1:.3f}")

def show_decision_tree_rules():
    """显示决策树规则"""
    print(f"\n决策树规则:")
    print("=" * 50)
    print("if GDP_per_capita >= 1.2:")
    print("    if Social_support >= 1.4:")
    print("        return 'High'")
    print("    elif Social_support >= 1.0:")
    print("        return 'Middle'")
    print("    else:")
    print("        return 'Middle'")
    print("elif GDP_per_capita >= 0.8:")
    print("    if Healthy_life_expectancy >= 0.8:")
    print("        return 'Middle'")
    print("    elif Social_support >= 1.2:")
    print("        return 'Middle'")
    print("    else:")
    print("        return 'Low'")
    print("else:")
    print("    if Social_support >= 1.0 and Freedom >= 0.4:")
    print("        return 'Middle'")
    print("    elif Social_support >= 0.8:")
    print("        return 'Low'")
    print("    else:")
    print("        return 'Low'")

def main():
    """主函数"""
    analyze_decision_tree()
    show_decision_tree_rules()
    
    print(f"\n总结:")
    print("- 决策树主要基于GDP、社会支持、健康预期寿命进行分类")
    print("- GDP是最重要的分类特征")
    print("- 社会支持是第二重要的特征")
    print("- 该模型能够较好地预测国家的幸福等级")

if __name__ == "__main__":
    main()
