#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据加载和基本处理
"""

import csv
from datetime import datetime

def test_load_apple():
    """测试加载Apple数据"""
    print("测试加载Apple数据...")
    try:
        with open('Apple Dataset.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            count = 0
            for row in reader:
                if count < 5:  # 只显示前5行
                    print(f"行 {count+1}: Date={row['Date']}, Close={row['Close']}")
                count += 1
                if count >= 10:  # 只处理前10行进行测试
                    break
            print(f"成功读取 {count} 行数据")
    except Exception as e:
        print(f"加载Apple数据失败: {e}")

def test_load_google():
    """测试加载Google数据"""
    print("\n测试加载Google数据...")
    try:
        with open('GOOGL.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            count = 0
            for row in reader:
                if count < 5:  # 只显示前5行
                    print(f"行 {count+1}: Date={row['Date']}, Close={row['Close']}")
                count += 1
                if count >= 10:  # 只处理前10行进行测试
                    break
            print(f"成功读取 {count} 行数据")
    except Exception as e:
        print(f"加载Google数据失败: {e}")

def test_load_tesla():
    """测试加载Tesla数据"""
    print("\n测试加载Tesla数据...")
    try:
        with open('Tesla.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            count = 0
            for row in reader:
                if count < 5:  # 只显示前5行
                    print(f"行 {count+1}: Date={row['Date']}, Close={row['Close']}")
                count += 1
                if count >= 10:  # 只处理前10行进行测试
                    break
            print(f"成功读取 {count} 行数据")
    except Exception as e:
        print(f"加载Tesla数据失败: {e}")

def test_moving_average():
    """测试移动平均线计算"""
    print("\n测试移动平均线计算...")
    
    # 模拟价格数据
    prices = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
    
    for i in range(4, len(prices)):  # 从第5个数据开始计算5日均线
        ma5 = sum(prices[i-4:i+1]) / 5
        print(f"第{i+1}天: 价格={prices[i]}, 5日均线={ma5:.2f}")
        
        if i >= 9:  # 从第10个数据开始计算10日均线
            ma10 = sum(prices[i-9:i+1]) / 10
            print(f"        10日均线={ma10:.2f}")

def main():
    print("股票数据加载测试")
    print("=" * 40)
    
    test_load_apple()
    test_load_google() 
    test_load_tesla()
    test_moving_average()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
