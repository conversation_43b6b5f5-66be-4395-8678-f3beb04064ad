-- MovieLens数据集分析查询
-- 数据库：假设使用SQLite/MySQL/PostgreSQL等支持CTE的数据库

-- 任务一：针对movielen数据集的分析查询

-- ========================================
-- 1. 列出平均得分前10的电影
-- ========================================

SELECT 
    m.movieId,
    m.title,
    ROUND(AVG(r.rating), 2) as avg_rating,
    COUNT(r.rating) as rating_count
FROM movies m
JOIN ratings r ON m.movieId = r.movieId
GROUP BY m.movieId, m.title
HAVING COUNT(r.rating) >= 10  -- 至少有10个评分才有统计意义
ORDER BY avg_rating DESC, rating_count DESC
LIMIT 10;

-- ========================================
-- 2. 列出每个类型的平均得分前10的电影
-- ========================================

-- 首先需要将genres字段拆分，创建电影-类型映射表
WITH movie_genres AS (
    SELECT 
        movieId,
        title,
        TRIM(genre) as genre
    FROM (
        -- 这里使用递归CTE来拆分genres字段
        -- 注意：不同数据库的字符串拆分语法可能不同
        SELECT 
            movieId,
            title,
            genres,
            CASE 
                WHEN INSTR(genres, '|') > 0 
                THEN SUBSTR(genres, 1, INSTR(genres, '|') - 1)
                ELSE genres
            END as genre,
            CASE 
                WHEN INSTR(genres, '|') > 0 
                THEN SUBSTR(genres, INSTR(genres, '|') + 1)
                ELSE ''
            END as remaining_genres
        FROM movies
        WHERE genres IS NOT NULL AND genres != ''
        
        UNION ALL
        
        SELECT 
            movieId,
            title,
            remaining_genres,
            CASE 
                WHEN INSTR(remaining_genres, '|') > 0 
                THEN SUBSTR(remaining_genres, 1, INSTR(remaining_genres, '|') - 1)
                ELSE remaining_genres
            END as genre,
            CASE 
                WHEN INSTR(remaining_genres, '|') > 0 
                THEN SUBSTR(remaining_genres, INSTR(remaining_genres, '|') + 1)
                ELSE ''
            END as remaining_genres
        FROM movie_genres
        WHERE remaining_genres != ''
    ) t
    WHERE genre != ''
),
genre_ratings AS (
    SELECT 
        mg.genre,
        mg.movieId,
        mg.title,
        ROUND(AVG(r.rating), 2) as avg_rating,
        COUNT(r.rating) as rating_count,
        ROW_NUMBER() OVER (PARTITION BY mg.genre ORDER BY AVG(r.rating) DESC, COUNT(r.rating) DESC) as rn
    FROM movie_genres mg
    JOIN ratings r ON mg.movieId = r.movieId
    GROUP BY mg.genre, mg.movieId, mg.title
    HAVING COUNT(r.rating) >= 5  -- 至少有5个评分
)
SELECT 
    genre,
    movieId,
    title,
    avg_rating,
    rating_count
FROM genre_ratings
WHERE rn <= 10
ORDER BY genre, rn;

-- ========================================
-- 3. 列出每个用户综合评价排在前5的电影类型
-- ========================================

WITH user_genre_ratings AS (
    SELECT 
        r.userId,
        mg.genre,
        ROUND(AVG(r.rating), 2) as avg_rating,
        COUNT(r.rating) as rating_count
    FROM ratings r
    JOIN movie_genres mg ON r.movieId = mg.movieId
    GROUP BY r.userId, mg.genre
    HAVING COUNT(r.rating) >= 3  -- 至少评价过3部该类型的电影
),
user_top_genres AS (
    SELECT 
        userId,
        genre,
        avg_rating,
        rating_count,
        ROW_NUMBER() OVER (PARTITION BY userId ORDER BY avg_rating DESC, rating_count DESC) as rn
    FROM user_genre_ratings
)
SELECT 
    userId,
    genre,
    avg_rating,
    rating_count
FROM user_top_genres
WHERE rn <= 5
ORDER BY userId, rn;

-- ========================================
-- 4. 列出每个用户观影次数排在前5的电影类型
-- ========================================

WITH user_genre_counts AS (
    SELECT 
        r.userId,
        mg.genre,
        COUNT(r.rating) as watch_count,
        ROUND(AVG(r.rating), 2) as avg_rating
    FROM ratings r
    JOIN movie_genres mg ON r.movieId = mg.movieId
    GROUP BY r.userId, mg.genre
),
user_top_watch_genres AS (
    SELECT 
        userId,
        genre,
        watch_count,
        avg_rating,
        ROW_NUMBER() OVER (PARTITION BY userId ORDER BY watch_count DESC, avg_rating DESC) as rn
    FROM user_genre_counts
)
SELECT 
    userId,
    genre,
    watch_count,
    avg_rating
FROM user_top_watch_genres
WHERE rn <= 5
ORDER BY userId, rn;
