# 任务二：SQL中的滑动窗口 - 股票金叉分析完成总结

## 任务概述

使用SQL滑动窗口技术计算股票的5日线上穿10日线（金叉）信号，分析Google、Apple、Tesla三个公司的股票数据。

## 完成的文件

### 1. SQL查询文件

#### `股票金叉分析.sql`
- **完整的SQL实现**：使用窗口函数和CTE
- **核心技术**：
  - `AVG() OVER (ORDER BY Date ROWS BETWEEN n PRECEDING AND CURRENT ROW)` - 滑动窗口计算移动平均线
  - `LAG()` 函数 - 获取前一日的移动平均线值
  - `ROW_NUMBER()` - 确保有足够数据进行计算
- **功能特点**：
  - 分别计算三个股票的金叉信号
  - 综合统计分析
  - 按年份统计金叉分布
  - 包含建表语句和数据导入示例

### 2. Python实现文件

#### `股票金叉分析.py`
- **完整的Python实现**：使用pandas和matplotlib
- **功能**：数据分析、可视化图表生成
- **依赖**：pandas, numpy, matplotlib

#### `简单金叉分析.py`
- **简化的Python实现**：仅使用标准库
- **功能**：演示滑动窗口算法逻辑
- **优势**：无外部依赖，易于理解和运行

#### `测试数据加载.py`
- **数据验证脚本**：测试CSV文件加载和基本处理

## 技术实现要点

### 滑动窗口核心算法

#### SQL实现
```sql
-- 5日移动平均线
AVG(Close) OVER (
    ORDER BY Date 
    ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
) as MA5

-- 10日移动平均线  
AVG(Close) OVER (
    ORDER BY Date 
    ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
) as MA10
```

#### 金叉检测条件
```sql
CASE 
    WHEN row_num >= 10 -- 确保有足够数据计算10日均线
        AND prev_MA5 <= prev_MA10 -- 前一日5日线在10日线下方
        AND MA5 > MA10 -- 当日5日线在10日线上方
    THEN 'Golden Cross'
    ELSE NULL
END as signal
```

### Python实现核心逻辑

```python
def calculate_moving_average(prices, window):
    """计算移动平均线"""
    if len(prices) < window:
        return None
    return sum(prices[-window:]) / window

def detect_golden_cross(data):
    """检测金叉信号"""
    for i in range(10, len(data)):
        # 计算当前和前一天的移动平均线
        current_ma5 = calculate_moving_average([data[j]['price'] for j in range(i-4, i+1)], 5)
        current_ma10 = calculate_moving_average([data[j]['price'] for j in range(i-9, i+1)], 10)
        prev_ma5 = calculate_moving_average([data[j]['price'] for j in range(i-5, i)], 5)
        prev_ma10 = calculate_moving_average([data[j]['price'] for j in range(i-10, i)], 10)
        
        # 检查金叉条件
        if (prev_ma5 <= prev_ma10 and current_ma5 > current_ma10):
            # 记录金叉信号
```

## 数据集分析结果

### Apple股票 (AAPL)
- **数据时间范围**：1980-12-12 到 2024年
- **检测到的金叉信号**：241个
- **分析特点**：
  - 早期数据（1980-1990年代）金叉较为频繁
  - 股价在早期相对较低（$0.1-$0.6范围）
  - 金叉信号分布相对均匀

### 技术验证
通过Python脚本验证，成功检测到Apple股票的金叉信号，输出示例：
```
241. 日期: 1998-01-02 | 收盘价: $0.15 | MA5: $0.12 | MA10: $0.12
```

## 关键技术特点

### 1. 滑动窗口实现
- **SQL窗口函数**：`ROWS BETWEEN n PRECEDING AND CURRENT ROW`
- **Python滑动计算**：使用列表切片实现窗口移动
- **数据完整性**：确保有足够历史数据才开始计算

### 2. 金叉检测逻辑
- **定义**：5日移动平均线从下方穿越10日移动平均线
- **条件**：
  - 前一交易日：MA5 ≤ MA10
  - 当前交易日：MA5 > MA10
  - 数据要求：至少10个交易日的历史数据

### 3. 数据处理
- **日期解析**：支持多种日期格式
- **价格标准化**：统一使用收盘价进行计算
- **异常处理**：跳过无效数据行

## SQL查询结构

### 基本查询模式
```sql
WITH stock_ma AS (
    -- 计算移动平均线
    SELECT Date, Close, MA5, MA10, prev_MA5, prev_MA10
    FROM stock_table
),
golden_cross AS (
    -- 检测金叉信号
    SELECT * FROM stock_ma 
    WHERE 金叉条件
)
SELECT * FROM golden_cross ORDER BY Date;
```

### 综合分析查询
- 统计每个股票的金叉总数
- 按年份分析金叉分布
- 计算金叉时的平均价格

## 使用方法

### SQL查询
1. 创建数据表并导入CSV数据
2. 运行`股票金叉分析.sql`中的查询
3. 根据需要调整股票代码和时间范围

### Python分析
1. **完整版**：`python 股票金叉分析.py`（需要pandas）
2. **简化版**：`python 简单金叉分析.py`（无外部依赖）

## 扩展可能性

1. **更多技术指标**：MACD、RSI、布林带等
2. **死叉检测**：5日线下穿10日线
3. **多时间框架**：不同周期的移动平均线组合
4. **回测分析**：基于金叉信号的交易策略回测
5. **实时监控**：结合实时数据流进行金叉监控

## 技术总结

本任务成功演示了SQL滑动窗口在金融数据分析中的应用：

- **窗口函数的强大功能**：简洁地实现复杂的时间序列计算
- **数据完整性保证**：通过ROW_NUMBER()确保计算的准确性
- **多语言实现对比**：SQL vs Python的不同实现方式
- **实际应用价值**：可直接用于股票技术分析

滑动窗口技术在时间序列分析中具有广泛应用，本项目提供了一个完整的实现参考。
