-- 任务四：基于SQL的决策树
-- 世界幸福指数数据集决策树分类算法
-- 目标：根据各项指标预测幸福指数等级（high, middle, low）

-- ========================================
-- 1. 创建数据表
-- ========================================

CREATE TABLE IF NOT EXISTS happiness (
    Overall_rank INT,
    Country VARCHAR(100),
    Score DECIMAL(5,3),
    GDP_per_capita DECIMAL(5,3),
    Social_support DECIMAL(5,3),
    Healthy_life_expectancy DECIMAL(5,3),
    Freedom_to_make_life_choices DECIMAL(5,3),
    Generosity DECIMAL(5,3),
    Perceptions_of_corruption DECIMAL(5,3)
);

-- ========================================
-- 2. 数据预处理：定义幸福指数等级
-- ========================================

-- 首先分析Score的分布，确定分类阈值
WITH score_stats AS (
    SELECT 
        MIN(Score) as min_score,
        MAX(Score) as max_score,
        AVG(Score) as avg_score,
        PERCENTILE_CONT(0.33) WITHIN GROUP (ORDER BY Score) as p33,
        PERCENTILE_CONT(0.67) WITHIN GROUP (ORDER BY Score) as p67
    FROM happiness
)
SELECT 
    'Score Distribution' as analysis,
    min_score,
    p33 as low_threshold,
    p67 as high_threshold,
    max_score
FROM score_stats;

-- 基于分析结果，定义分类标准：
-- Low: Score < 4.5
-- Middle: 4.5 <= Score < 6.5  
-- High: Score >= 6.5

-- ========================================
-- 3. 创建带分类标签的视图
-- ========================================

CREATE VIEW happiness_classified AS
SELECT 
    *,
    CASE 
        WHEN Score < 4.5 THEN 'Low'
        WHEN Score < 6.5 THEN 'Middle'
        ELSE 'High'
    END as Happiness_Level
FROM happiness;

-- ========================================
-- 4. 决策树算法实现
-- ========================================

-- 4.1 计算信息熵函数（使用CTE模拟）
WITH entropy_calculation AS (
    SELECT 
        Happiness_Level,
        COUNT(*) as count,
        COUNT(*) * 1.0 / (SELECT COUNT(*) FROM happiness_classified) as probability
    FROM happiness_classified
    GROUP BY Happiness_Level
),
total_entropy AS (
    SELECT 
        -SUM(probability * LOG2(probability)) as entropy
    FROM entropy_calculation
)
SELECT 
    'Total Entropy' as metric,
    ROUND(entropy, 4) as value
FROM total_entropy;

-- 4.2 计算各属性的信息增益
-- 以GDP_per_capita为例，找到最佳分割点

WITH gdp_splits AS (
    SELECT DISTINCT
        GDP_per_capita as split_value
    FROM happiness_classified
    ORDER BY GDP_per_capita
),
split_analysis AS (
    SELECT 
        g.split_value,
        -- 左分支（<= split_value）
        COUNT(CASE WHEN h.GDP_per_capita <= g.split_value THEN 1 END) as left_count,
        COUNT(CASE WHEN h.GDP_per_capita <= g.split_value AND h.Happiness_Level = 'Low' THEN 1 END) as left_low,
        COUNT(CASE WHEN h.GDP_per_capita <= g.split_value AND h.Happiness_Level = 'Middle' THEN 1 END) as left_middle,
        COUNT(CASE WHEN h.GDP_per_capita <= g.split_value AND h.Happiness_Level = 'High' THEN 1 END) as left_high,
        
        -- 右分支（> split_value）
        COUNT(CASE WHEN h.GDP_per_capita > g.split_value THEN 1 END) as right_count,
        COUNT(CASE WHEN h.GDP_per_capita > g.split_value AND h.Happiness_Level = 'Low' THEN 1 END) as right_low,
        COUNT(CASE WHEN h.GDP_per_capita > g.split_value AND h.Happiness_Level = 'Middle' THEN 1 END) as right_middle,
        COUNT(CASE WHEN h.GDP_per_capita > g.split_value AND h.Happiness_Level = 'High' THEN 1 END) as right_high,
        
        COUNT(*) as total_count
    FROM gdp_splits g
    CROSS JOIN happiness_classified h
    GROUP BY g.split_value
    HAVING COUNT(CASE WHEN h.GDP_per_capita <= g.split_value THEN 1 END) > 0 
       AND COUNT(CASE WHEN h.GDP_per_capita > g.split_value THEN 1 END) > 0
)
SELECT 
    split_value,
    left_count,
    right_count,
    -- 简化的纯度计算（基于主要类别的比例）
    CASE 
        WHEN left_count > 0 THEN
            GREATEST(left_low, left_middle, left_high) * 1.0 / left_count
        ELSE 0
    END as left_purity,
    CASE 
        WHEN right_count > 0 THEN
            GREATEST(right_low, right_middle, right_high) * 1.0 / right_count
        ELSE 0
    END as right_purity,
    -- 加权纯度
    (left_count * 1.0 / total_count) * 
    CASE WHEN left_count > 0 THEN GREATEST(left_low, left_middle, left_high) * 1.0 / left_count ELSE 0 END +
    (right_count * 1.0 / total_count) * 
    CASE WHEN right_count > 0 THEN GREATEST(right_low, right_middle, right_high) * 1.0 / right_count ELSE 0 END as weighted_purity
FROM split_analysis
ORDER BY weighted_purity DESC
LIMIT 10;

-- ========================================
-- 5. 构建简化决策树
-- ========================================

-- 基于数据分析，构建决策树规则
WITH decision_tree_rules AS (
    SELECT 
        Country,
        Score,
        GDP_per_capita,
        Social_support,
        Healthy_life_expectancy,
        Freedom_to_make_life_choices,
        Generosity,
        Perceptions_of_corruption,
        Happiness_Level as actual_level,
        
        -- 决策树分类规则
        CASE 
            -- 第一层：GDP_per_capita
            WHEN GDP_per_capita >= 1.2 THEN
                CASE 
                    -- 高GDP分支：看社会支持
                    WHEN Social_support >= 1.4 THEN 'High'
                    WHEN Social_support >= 1.0 THEN 'Middle'
                    ELSE 'Middle'
                END
            WHEN GDP_per_capita >= 0.8 THEN
                CASE 
                    -- 中等GDP分支：看健康预期寿命
                    WHEN Healthy_life_expectancy >= 0.8 THEN 'Middle'
                    WHEN Social_support >= 1.2 THEN 'Middle'
                    ELSE 'Low'
                END
            ELSE
                CASE 
                    -- 低GDP分支：看社会支持和自由度
                    WHEN Social_support >= 1.0 AND Freedom_to_make_life_choices >= 0.4 THEN 'Middle'
                    WHEN Social_support >= 0.8 THEN 'Low'
                    ELSE 'Low'
                END
        END as predicted_level
    FROM happiness_classified
)
SELECT 
    'Decision Tree Classification Results' as analysis,
    COUNT(*) as total_samples,
    SUM(CASE WHEN actual_level = predicted_level THEN 1 ELSE 0 END) as correct_predictions,
    ROUND(
        SUM(CASE WHEN actual_level = predicted_level THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 
        2
    ) as accuracy_percentage
FROM decision_tree_rules;

-- ========================================
-- 6. 详细分类结果分析
-- ========================================

WITH decision_tree_results AS (
    SELECT 
        Country,
        Score,
        GDP_per_capita,
        Social_support,
        Healthy_life_expectancy,
        Happiness_Level as actual_level,
        
        -- 应用决策树规则
        CASE 
            WHEN GDP_per_capita >= 1.2 THEN
                CASE 
                    WHEN Social_support >= 1.4 THEN 'High'
                    WHEN Social_support >= 1.0 THEN 'Middle'
                    ELSE 'Middle'
                END
            WHEN GDP_per_capita >= 0.8 THEN
                CASE 
                    WHEN Healthy_life_expectancy >= 0.8 THEN 'Middle'
                    WHEN Social_support >= 1.2 THEN 'Middle'
                    ELSE 'Low'
                END
            ELSE
                CASE 
                    WHEN Social_support >= 1.0 AND Freedom_to_make_life_choices >= 0.4 THEN 'Middle'
                    WHEN Social_support >= 0.8 THEN 'Low'
                    ELSE 'Low'
                END
        END as predicted_level
    FROM happiness_classified
)
SELECT 
    actual_level,
    predicted_level,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM decision_tree_results
GROUP BY actual_level, predicted_level
ORDER BY actual_level, predicted_level;

-- ========================================
-- 7. 混淆矩阵
-- ========================================

WITH confusion_matrix AS (
    SELECT 
        Happiness_Level as actual,
        CASE 
            WHEN GDP_per_capita >= 1.2 THEN
                CASE 
                    WHEN Social_support >= 1.4 THEN 'High'
                    WHEN Social_support >= 1.0 THEN 'Middle'
                    ELSE 'Middle'
                END
            WHEN GDP_per_capita >= 0.8 THEN
                CASE 
                    WHEN Healthy_life_expectancy >= 0.8 THEN 'Middle'
                    WHEN Social_support >= 1.2 THEN 'Middle'
                    ELSE 'Low'
                END
            ELSE
                CASE 
                    WHEN Social_support >= 1.0 AND Freedom_to_make_life_choices >= 0.4 THEN 'Middle'
                    WHEN Social_support >= 0.8 THEN 'Low'
                    ELSE 'Low'
                END
        END as predicted
    FROM happiness_classified
)
SELECT 
    'Confusion Matrix' as matrix_type,
    SUM(CASE WHEN actual = 'High' AND predicted = 'High' THEN 1 ELSE 0 END) as High_High,
    SUM(CASE WHEN actual = 'High' AND predicted = 'Middle' THEN 1 ELSE 0 END) as High_Middle,
    SUM(CASE WHEN actual = 'High' AND predicted = 'Low' THEN 1 ELSE 0 END) as High_Low,
    SUM(CASE WHEN actual = 'Middle' AND predicted = 'High' THEN 1 ELSE 0 END) as Middle_High,
    SUM(CASE WHEN actual = 'Middle' AND predicted = 'Middle' THEN 1 ELSE 0 END) as Middle_Middle,
    SUM(CASE WHEN actual = 'Middle' AND predicted = 'Low' THEN 1 ELSE 0 END) as Middle_Low,
    SUM(CASE WHEN actual = 'Low' AND predicted = 'High' THEN 1 ELSE 0 END) as Low_High,
    SUM(CASE WHEN actual = 'Low' AND predicted = 'Middle' THEN 1 ELSE 0 END) as Low_Middle,
    SUM(CASE WHEN actual = 'Low' AND predicted = 'Low' THEN 1 ELSE 0 END) as Low_Low
FROM confusion_matrix;
