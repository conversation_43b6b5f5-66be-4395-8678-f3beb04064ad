#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
世界幸福指数决策树分析
使用Python实现决策树算法，验证SQL实现的正确性
"""

import csv
import math
from collections import defaultdict, Counter

def load_happiness_data(filename):
    """加载幸福指数数据"""
    data = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # 转换数值字段
                processed_row = {
                    'Country': row['Country or region'],
                    'Score': float(row['Score']),
                    'GDP_per_capita': float(row['GDP per capita']),
                    'Social_support': float(row['Social support']),
                    'Healthy_life_expectancy': float(row['Healthy life expectancy']),
                    'Freedom_to_make_life_choices': float(row['Freedom to make life choices']),
                    'Generosity': float(row['Generosity']),
                    'Perceptions_of_corruption': float(row['Perceptions of corruption'])
                }
                data.append(processed_row)
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return []

def classify_happiness_level(score):
    """根据分数分类幸福等级"""
    if score < 4.5:
        return 'Low'
    elif score < 6.5:
        return 'Middle'
    else:
        return 'High'

def calculate_entropy(labels):
    """计算信息熵"""
    if not labels:
        return 0
    
    label_counts = Counter(labels)
    total = len(labels)
    entropy = 0
    
    for count in label_counts.values():
        if count > 0:
            probability = count / total
            entropy -= probability * math.log2(probability)
    
    return entropy

def calculate_information_gain(data, feature, target_feature):
    """计算信息增益"""
    # 计算总熵
    all_labels = [row[target_feature] for row in data]
    total_entropy = calculate_entropy(all_labels)
    
    # 按特征值分组
    feature_groups = defaultdict(list)
    for row in data:
        feature_groups[row[feature]].append(row[target_feature])
    
    # 计算加权熵
    total_samples = len(data)
    weighted_entropy = 0
    
    for group_labels in feature_groups.values():
        group_size = len(group_labels)
        group_entropy = calculate_entropy(group_labels)
        weighted_entropy += (group_size / total_samples) * group_entropy
    
    return total_entropy - weighted_entropy

def find_best_split_numeric(data, feature, target_feature):
    """为数值特征找到最佳分割点"""
    # 获取所有可能的分割点
    values = sorted(set(row[feature] for row in data))
    best_gain = -1
    best_split = None
    
    for i in range(len(values) - 1):
        split_value = (values[i] + values[i + 1]) / 2
        
        # 分割数据
        left_data = [row for row in data if row[feature] <= split_value]
        right_data = [row for row in data if row[feature] > split_value]
        
        if len(left_data) == 0 or len(right_data) == 0:
            continue
        
        # 计算信息增益
        left_labels = [row[target_feature] for row in left_data]
        right_labels = [row[target_feature] for row in right_data]
        
        total_entropy = calculate_entropy([row[target_feature] for row in data])
        left_entropy = calculate_entropy(left_labels)
        right_entropy = calculate_entropy(right_labels)
        
        weighted_entropy = (len(left_data) / len(data)) * left_entropy + \
                          (len(right_data) / len(data)) * right_entropy
        
        gain = total_entropy - weighted_entropy
        
        if gain > best_gain:
            best_gain = gain
            best_split = split_value
    
    return best_split, best_gain

def build_simple_decision_tree(data):
    """构建简化的决策树"""
    # 添加分类标签
    for row in data:
        row['Happiness_Level'] = classify_happiness_level(row['Score'])
    
    print("构建决策树...")
    print("=" * 50)
    
    # 分析各特征的信息增益
    features = ['GDP_per_capita', 'Social_support', 'Healthy_life_expectancy', 
                'Freedom_to_make_life_choices', 'Generosity', 'Perceptions_of_corruption']
    
    print("特征信息增益分析:")
    feature_gains = {}
    
    for feature in features:
        split_value, gain = find_best_split_numeric(data, feature, 'Happiness_Level')
        feature_gains[feature] = (split_value, gain)
        print(f"{feature:<30}: 最佳分割点={split_value:.3f}, 信息增益={gain:.4f}")
    
    # 选择最佳特征作为根节点
    best_feature = max(feature_gains.keys(), key=lambda x: feature_gains[x][1])
    best_split_value = feature_gains[best_feature][0]
    
    print(f"\n选择根节点特征: {best_feature} (分割点: {best_split_value:.3f})")
    
    return best_feature, best_split_value, feature_gains

def apply_decision_tree_rules(row):
    """应用决策树规则进行分类"""
    gdp = row['GDP_per_capita']
    social = row['Social_support']
    health = row['Healthy_life_expectancy']
    freedom = row['Freedom_to_make_life_choices']
    
    # 基于分析结果的决策树规则
    if gdp >= 1.2:
        if social >= 1.4:
            return 'High'
        elif social >= 1.0:
            return 'Middle'
        else:
            return 'Middle'
    elif gdp >= 0.8:
        if health >= 0.8:
            return 'Middle'
        elif social >= 1.2:
            return 'Middle'
        else:
            return 'Low'
    else:
        if social >= 1.0 and freedom >= 0.4:
            return 'Middle'
        elif social >= 0.8:
            return 'Low'
        else:
            return 'Low'

def evaluate_decision_tree(data):
    """评估决策树性能"""
    correct = 0
    total = len(data)
    
    confusion_matrix = defaultdict(lambda: defaultdict(int))
    
    print("\n决策树分类结果:")
    print("=" * 80)
    print(f"{'国家':<20} {'实际':<8} {'预测':<8} {'分数':<6} {'GDP':<6} {'社会支持':<8}")
    print("-" * 80)
    
    for row in data:
        actual = row['Happiness_Level']
        predicted = apply_decision_tree_rules(row)
        
        if actual == predicted:
            correct += 1
        
        confusion_matrix[actual][predicted] += 1
        
        # 显示前20个结果
        if len([r for r in data if data.index(r) <= data.index(row)]) <= 20:
            print(f"{row['Country']:<20} {actual:<8} {predicted:<8} {row['Score']:<6.2f} "
                  f"{row['GDP_per_capita']:<6.3f} {row['Social_support']:<8.3f}")
    
    accuracy = correct / total * 100
    print(f"\n准确率: {correct}/{total} = {accuracy:.2f}%")
    
    # 显示混淆矩阵
    print(f"\n混淆矩阵:")
    print(f"{'实际\\预测':<12} {'High':<8} {'Middle':<8} {'Low':<8}")
    print("-" * 40)
    
    for actual in ['High', 'Middle', 'Low']:
        print(f"{actual:<12} ", end="")
        for predicted in ['High', 'Middle', 'Low']:
            print(f"{confusion_matrix[actual][predicted]:<8}", end="")
        print()
    
    return accuracy, confusion_matrix

def analyze_feature_importance(data):
    """分析特征重要性"""
    print(f"\n特征重要性分析:")
    print("=" * 50)
    
    features = ['GDP_per_capita', 'Social_support', 'Healthy_life_expectancy', 
                'Freedom_to_make_life_choices', 'Generosity', 'Perceptions_of_corruption']
    
    # 计算各特征与幸福等级的相关性
    for feature in features:
        high_avg = sum(row[feature] for row in data if row['Happiness_Level'] == 'High') / \
                   max(1, len([row for row in data if row['Happiness_Level'] == 'High']))
        
        middle_avg = sum(row[feature] for row in data if row['Happiness_Level'] == 'Middle') / \
                     max(1, len([row for row in data if row['Happiness_Level'] == 'Middle']))
        
        low_avg = sum(row[feature] for row in data if row['Happiness_Level'] == 'Low') / \
                  max(1, len([row for row in data if row['Happiness_Level'] == 'Low']))
        
        print(f"{feature:<30}:")
        print(f"  High级别平均值:   {high_avg:.3f}")
        print(f"  Middle级别平均值: {middle_avg:.3f}")
        print(f"  Low级别平均值:    {low_avg:.3f}")
        print(f"  差异程度:         {high_avg - low_avg:.3f}")
        print()

def main():
    """主函数"""
    print("世界幸福指数决策树分析")
    print("=" * 50)
    
    # 加载数据
    data = load_happiness_data('世界幸福指数数据集/2019.csv')
    
    if not data:
        print("数据加载失败")
        return
    
    print(f"加载了 {len(data)} 个国家的数据")
    
    # 添加分类标签
    for row in data:
        row['Happiness_Level'] = classify_happiness_level(row['Score'])
    
    # 统计各等级数量
    level_counts = Counter(row['Happiness_Level'] for row in data)
    print(f"\n幸福等级分布:")
    for level, count in level_counts.items():
        print(f"  {level}: {count} 个国家")
    
    # 构建决策树
    best_feature, best_split, feature_gains = build_simple_decision_tree(data)
    
    # 分析特征重要性
    analyze_feature_importance(data)
    
    # 评估决策树
    accuracy, confusion_matrix = evaluate_decision_tree(data)
    
    print(f"\n总结:")
    print(f"- 使用的主要特征: GDP per capita, Social support, Healthy life expectancy")
    print(f"- 决策树准确率: {accuracy:.2f}%")
    print(f"- 该模型可以较好地区分不同幸福等级的国家")

if __name__ == "__main__":
    main()
