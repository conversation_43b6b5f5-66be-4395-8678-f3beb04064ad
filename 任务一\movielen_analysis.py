#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MovieLens数据集分析脚本
使用Python和pandas来模拟SQL查询分析
"""

import pandas as pd
import numpy as np
from collections import defaultdict

def load_data():
    """加载MovieLens数据"""
    print("正在加载数据...")
    
    # 加载电影数据
    movies = pd.read_csv('movielen数据集/movies.csv')
    print(f"加载了 {len(movies)} 部电影")
    
    # 加载评分数据
    ratings = pd.read_csv('movielen数据集/ratings.csv')
    print(f"加载了 {len(ratings)} 条评分记录")
    
    return movies, ratings

def task1_top_rated_movies(movies, ratings, min_ratings=10):
    """
    任务1：列出平均得分前10的电影
    """
    print("\n========================================")
    print("任务1：平均得分前10的电影")
    print("========================================")
    
    # 计算每部电影的平均评分和评分数量
    movie_stats = ratings.groupby('movieId').agg({
        'rating': ['mean', 'count']
    }).round(2)
    
    movie_stats.columns = ['avg_rating', 'rating_count']
    movie_stats = movie_stats.reset_index()
    
    # 过滤掉评分数量少的电影
    movie_stats = movie_stats[movie_stats['rating_count'] >= min_ratings]
    
    # 合并电影信息
    result = movie_stats.merge(movies[['movieId', 'title']], on='movieId')
    
    # 按平均评分排序，取前10
    top_movies = result.sort_values(['avg_rating', 'rating_count'], ascending=[False, False]).head(10)
    
    print(f"平均得分前10的电影（至少{min_ratings}个评分）：")
    for i, row in top_movies.iterrows():
        print(f"{row.name+1:2d}. {row['title']:<50} 平均评分: {row['avg_rating']:.2f} ({row['rating_count']}个评分)")
    
    return top_movies

def expand_genres(movies):
    """
    将电影类型字段展开为电影-类型映射
    """
    movie_genre_list = []
    
    for _, movie in movies.iterrows():
        if pd.notna(movie['genres']) and movie['genres'] != '(no genres listed)':
            genres = movie['genres'].split('|')
            for genre in genres:
                movie_genre_list.append({
                    'movieId': movie['movieId'],
                    'title': movie['title'],
                    'genre': genre.strip()
                })
    
    return pd.DataFrame(movie_genre_list)

def task2_top_movies_by_genre(movies, ratings, min_ratings=5):
    """
    任务2：列出每个类型的平均得分前10的电影
    """
    print("\n========================================")
    print("任务2：每个类型的平均得分前10的电影")
    print("========================================")
    
    # 展开电影类型
    movie_genres = expand_genres(movies)
    
    # 计算每部电影的平均评分
    movie_stats = ratings.groupby('movieId').agg({
        'rating': ['mean', 'count']
    }).round(2)
    movie_stats.columns = ['avg_rating', 'rating_count']
    movie_stats = movie_stats.reset_index()
    
    # 合并数据
    genre_movies = movie_genres.merge(movie_stats, on='movieId')
    genre_movies = genre_movies[genre_movies['rating_count'] >= min_ratings]
    
    # 按类型分组，取每个类型的前10部电影
    top_genres = ['Action', 'Comedy', 'Drama', 'Thriller', 'Romance', 'Sci-Fi']
    
    for genre in top_genres:
        genre_data = genre_movies[genre_movies['genre'] == genre]
        if len(genre_data) > 0:
            top_movies = genre_data.sort_values(['avg_rating', 'rating_count'], 
                                              ascending=[False, False]).head(10)
            
            print(f"\n{genre}类型电影平均得分前10：")
            for i, row in top_movies.iterrows():
                print(f"  {len(top_movies)-len(top_movies)+i+1:2d}. {row['title']:<45} 评分: {row['avg_rating']:.2f} ({row['rating_count']}个)")

def task3_user_favorite_genres(movies, ratings, min_movies=3):
    """
    任务3：列出每个用户综合评价排在前5的电影类型
    """
    print("\n========================================")
    print("任务3：每个用户综合评价排在前5的电影类型")
    print("========================================")
    
    # 展开电影类型
    movie_genres = expand_genres(movies)
    
    # 合并评分和类型数据
    user_genre_ratings = ratings.merge(movie_genres, on='movieId')
    
    # 计算每个用户对每个类型的平均评分和观影数量
    user_genre_stats = user_genre_ratings.groupby(['userId', 'genre']).agg({
        'rating': ['mean', 'count']
    }).round(2)
    user_genre_stats.columns = ['avg_rating', 'movie_count']
    user_genre_stats = user_genre_stats.reset_index()
    
    # 过滤掉观影数量少的记录
    user_genre_stats = user_genre_stats[user_genre_stats['movie_count'] >= min_movies]
    
    # 显示前5个用户的结果
    print(f"前5个用户综合评价最高的电影类型（至少观看{min_movies}部）：")
    
    for user_id in sorted(user_genre_stats['userId'].unique())[:5]:
        user_data = user_genre_stats[user_genre_stats['userId'] == user_id]
        top_genres = user_data.sort_values(['avg_rating', 'movie_count'], 
                                         ascending=[False, False]).head(5)
        
        print(f"\n用户 {user_id}:")
        for i, row in top_genres.iterrows():
            print(f"  {i+1}. {row['genre']:<15} 平均评分: {row['avg_rating']:.2f} ({row['movie_count']}部电影)")

def task4_user_most_watched_genres(movies, ratings):
    """
    任务4：列出每个用户观影次数排在前5的电影类型
    """
    print("\n========================================")
    print("任务4：每个用户观影次数排在前5的电影类型")
    print("========================================")
    
    # 展开电影类型
    movie_genres = expand_genres(movies)
    
    # 合并评分和类型数据
    user_genre_ratings = ratings.merge(movie_genres, on='movieId')
    
    # 计算每个用户对每个类型的观影次数和平均评分
    user_genre_stats = user_genre_ratings.groupby(['userId', 'genre']).agg({
        'rating': ['count', 'mean']
    }).round(2)
    user_genre_stats.columns = ['watch_count', 'avg_rating']
    user_genre_stats = user_genre_stats.reset_index()
    
    # 显示前5个用户的结果
    print("前5个用户观影次数最多的电影类型：")
    
    for user_id in sorted(user_genre_stats['userId'].unique())[:5]:
        user_data = user_genre_stats[user_genre_stats['userId'] == user_id]
        top_genres = user_data.sort_values(['watch_count', 'avg_rating'], 
                                         ascending=[False, False]).head(5)
        
        print(f"\n用户 {user_id}:")
        for i, row in top_genres.iterrows():
            print(f"  {i+1}. {row['genre']:<15} 观影次数: {row['watch_count']:3d} (平均评分: {row['avg_rating']:.2f})")

def main():
    """主函数"""
    try:
        # 加载数据
        movies, ratings = load_data()
        
        # 执行四个分析任务
        task1_top_rated_movies(movies, ratings)
        task2_top_movies_by_genre(movies, ratings)
        task3_user_favorite_genres(movies, ratings)
        task4_user_most_watched_genres(movies, ratings)
        
        print("\n分析完成！")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
