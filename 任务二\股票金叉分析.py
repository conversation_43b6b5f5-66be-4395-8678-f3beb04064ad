#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票金叉分析 - 使用Python实现滑动窗口计算
计算5日线上穿10日线（金叉）出现的位置
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_stock_data():
    """加载三个股票的数据"""
    try:
        # 加载Apple数据
        apple = pd.read_csv('Apple Dataset.csv')
        apple['Date'] = pd.to_datetime(apple['Date'])
        apple = apple.sort_values('Date').reset_index(drop=True)
        
        # 加载Google数据
        google = pd.read_csv('GOOGL.csv')
        google['Date'] = pd.to_datetime(google['Date'])
        google = google.sort_values('Date').reset_index(drop=True)
        
        # 加载Tesla数据
        tesla = pd.read_csv('Tesla.csv')
        tesla['Date'] = pd.to_datetime(tesla['Date'])
        tesla = tesla.sort_values('Date').reset_index(drop=True)
        
        return apple, google, tesla
    except Exception as e:
        print(f"数据加载错误: {e}")
        return None, None, None

def calculate_moving_averages(df, price_col='Close'):
    """计算移动平均线"""
    df = df.copy()
    
    # 计算5日和10日移动平均线
    df['MA5'] = df[price_col].rolling(window=5, min_periods=5).mean()
    df['MA10'] = df[price_col].rolling(window=10, min_periods=10).mean()
    
    # 计算前一日的移动平均线
    df['prev_MA5'] = df['MA5'].shift(1)
    df['prev_MA10'] = df['MA10'].shift(1)
    
    return df

def detect_golden_cross(df):
    """检测金叉信号"""
    df = df.copy()
    
    # 金叉条件：
    # 1. 前一日5日线在10日线下方或相等
    # 2. 当日5日线在10日线上方
    # 3. 确保有足够的数据（至少10个交易日）
    
    golden_cross_condition = (
        (df.index >= 9) &  # 确保有足够数据计算10日均线
        (df['prev_MA5'] <= df['prev_MA10']) &  # 前一日5日线不高于10日线
        (df['MA5'] > df['MA10']) &  # 当日5日线高于10日线
        df['MA5'].notna() & df['MA10'].notna() &  # 确保数据有效
        df['prev_MA5'].notna() & df['prev_MA10'].notna()
    )
    
    df['Golden_Cross'] = golden_cross_condition
    
    return df

def analyze_stock(df, stock_name, price_col='Close'):
    """分析单个股票的金叉情况"""
    print(f"\n========================================")
    print(f"{stock_name} 股票金叉分析")
    print(f"========================================")
    
    # 计算移动平均线
    df_analyzed = calculate_moving_averages(df, price_col)
    
    # 检测金叉
    df_analyzed = detect_golden_cross(df_analyzed)
    
    # 提取金叉点
    golden_crosses = df_analyzed[df_analyzed['Golden_Cross']].copy()
    
    if len(golden_crosses) > 0:
        print(f"发现 {len(golden_crosses)} 个金叉信号：")
        print("-" * 80)
        
        for i, (idx, row) in enumerate(golden_crosses.iterrows(), 1):
            print(f"{i:2d}. 日期: {row['Date'].strftime('%Y-%m-%d')} | "
                  f"收盘价: ${row[price_col]:8.2f} | "
                  f"MA5: ${row['MA5']:8.2f} | "
                  f"MA10: ${row['MA10']:8.2f}")
        
        # 统计信息
        print(f"\n统计信息:")
        print(f"- 金叉总数: {len(golden_crosses)}")
        print(f"- 首次金叉: {golden_crosses.iloc[0]['Date'].strftime('%Y-%m-%d')}")
        print(f"- 最后金叉: {golden_crosses.iloc[-1]['Date'].strftime('%Y-%m-%d')}")
        print(f"- 金叉时平均价格: ${golden_crosses[price_col].mean():.2f}")
        print(f"- 金叉时最高价格: ${golden_crosses[price_col].max():.2f}")
        print(f"- 金叉时最低价格: ${golden_crosses[price_col].min():.2f}")
        
        # 按年份统计
        golden_crosses['Year'] = golden_crosses['Date'].dt.year
        yearly_stats = golden_crosses.groupby('Year').size()
        print(f"\n按年份统计:")
        for year, count in yearly_stats.items():
            print(f"- {year}年: {count}次金叉")
    else:
        print("未发现金叉信号")
    
    return df_analyzed, golden_crosses

def plot_stock_analysis(df, golden_crosses, stock_name, price_col='Close'):
    """绘制股票分析图表"""
    plt.figure(figsize=(15, 8))
    
    # 绘制价格和移动平均线
    plt.plot(df['Date'], df[price_col], label=f'{stock_name} 收盘价', alpha=0.7, linewidth=1)
    plt.plot(df['Date'], df['MA5'], label='5日移动平均线', linewidth=2)
    plt.plot(df['Date'], df['MA10'], label='10日移动平均线', linewidth=2)
    
    # 标记金叉点
    if len(golden_crosses) > 0:
        plt.scatter(golden_crosses['Date'], golden_crosses[price_col], 
                   color='red', s=100, marker='^', label='金叉信号', zorder=5)
    
    plt.title(f'{stock_name} 股票价格走势与金叉信号', fontsize=16)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('价格 ($)', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(f'{stock_name}_golden_cross_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def comprehensive_analysis(apple_crosses, google_crosses, tesla_crosses):
    """综合分析三个股票的金叉情况"""
    print(f"\n========================================")
    print(f"综合分析：三个股票金叉对比")
    print(f"========================================")
    
    # 统计总数
    total_stats = {
        'AAPL': len(apple_crosses),
        'GOOGL': len(google_crosses),
        'TSLA': len(tesla_crosses)
    }
    
    print("金叉次数统计:")
    for stock, count in total_stats.items():
        print(f"- {stock}: {count}次")
    
    print(f"- 总计: {sum(total_stats.values())}次")
    
    # 找出金叉最多的股票
    max_stock = max(total_stats, key=total_stats.get)
    print(f"\n金叉次数最多的股票: {max_stock} ({total_stats[max_stock]}次)")

def main():
    """主函数"""
    print("股票金叉分析 - 滑动窗口实现")
    print("=" * 50)
    
    # 加载数据
    apple, google, tesla = load_stock_data()
    
    if apple is None:
        print("数据加载失败，请检查CSV文件是否存在")
        return
    
    # 分析各个股票
    apple_analyzed, apple_crosses = analyze_stock(apple, "Apple (AAPL)")
    google_analyzed, google_crosses = analyze_stock(google, "Google (GOOGL)")
    tesla_analyzed, tesla_crosses = analyze_stock(tesla, "Tesla (TSLA)")
    
    # 综合分析
    comprehensive_analysis(apple_crosses, google_crosses, tesla_crosses)
    
    # 可选：绘制图表（需要matplotlib）
    try:
        print(f"\n正在生成图表...")
        plot_stock_analysis(apple_analyzed, apple_crosses, "Apple")
        plot_stock_analysis(google_analyzed, google_crosses, "Google")
        plot_stock_analysis(tesla_analyzed, tesla_crosses, "Tesla")
        print("图表已保存到当前目录")
    except Exception as e:
        print(f"图表生成失败: {e}")
    
    print(f"\n分析完成！")

if __name__ == "__main__":
    main()
