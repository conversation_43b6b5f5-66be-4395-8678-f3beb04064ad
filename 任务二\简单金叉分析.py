#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票金叉分析 - 简化版（不依赖外部库）
演示滑动窗口计算和金叉检测的基本逻辑
"""

import csv
from datetime import datetime

def load_csv_data(filename):
    """加载CSV数据"""
    data = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(row)
        return data
    except Exception as e:
        print(f"加载 {filename} 失败: {e}")
        return []

def parse_stock_data(raw_data, date_col='Date', price_col='Close'):
    """解析股票数据"""
    parsed_data = []

    for row in raw_data:
        try:
            # 解析日期 - 处理不同的日期格式
            date_str = row[date_col].split(' ')[0]  # 去掉时间部分

            # 尝试不同的日期格式
            date = None
            for fmt in ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y']:
                try:
                    date = datetime.strptime(date_str, fmt)
                    break
                except ValueError:
                    continue

            if date is None:
                print(f"无法解析日期: {date_str}")
                continue

            # 解析价格
            price = float(row[price_col])

            parsed_data.append({
                'date': date,
                'price': price,
                'raw_row': row
            })
        except Exception as e:
            print(f"解析数据行失败: {e}, 跳过该行")
            continue

    # 按日期排序
    parsed_data.sort(key=lambda x: x['date'])
    return parsed_data

def calculate_moving_average(prices, window):
    """计算移动平均线"""
    if len(prices) < window:
        return None
    return sum(prices[-window:]) / window

def detect_golden_cross_simple(data):
    """检测金叉信号 - 简化版"""
    golden_crosses = []
    
    for i in range(10, len(data)):  # 从第10个数据点开始（确保有足够数据计算10日均线）
        # 获取当前和前一天的价格数据
        current_prices = [data[j]['price'] for j in range(i-9, i+1)]  # 最近10天
        prev_prices = [data[j]['price'] for j in range(i-10, i)]      # 前一天的最近10天
        
        # 计算当前的5日和10日均线
        current_ma5 = calculate_moving_average([data[j]['price'] for j in range(i-4, i+1)], 5)
        current_ma10 = calculate_moving_average(current_prices, 10)
        
        # 计算前一天的5日和10日均线
        prev_ma5 = calculate_moving_average([data[j]['price'] for j in range(i-5, i)], 5)
        prev_ma10 = calculate_moving_average(prev_prices, 10)
        
        # 检查金叉条件
        if (current_ma5 and current_ma10 and prev_ma5 and prev_ma10 and
            prev_ma5 <= prev_ma10 and current_ma5 > current_ma10):
            
            golden_crosses.append({
                'date': data[i]['date'],
                'price': data[i]['price'],
                'ma5': current_ma5,
                'ma10': current_ma10,
                'index': i
            })
    
    return golden_crosses

def analyze_stock_simple(filename, stock_name):
    """分析单个股票的金叉情况 - 简化版"""
    print(f"\n========================================")
    print(f"{stock_name} 股票金叉分析")
    print(f"========================================")
    
    # 加载数据
    raw_data = load_csv_data(filename)
    if not raw_data:
        print(f"无法加载 {filename}")
        return []
    
    # 解析数据
    data = parse_stock_data(raw_data)
    if not data:
        print(f"无法解析 {filename} 中的数据")
        return []
    
    print(f"加载了 {len(data)} 条数据记录")
    print(f"数据时间范围: {data[0]['date'].strftime('%Y-%m-%d')} 到 {data[-1]['date'].strftime('%Y-%m-%d')}")
    
    # 检测金叉
    golden_crosses = detect_golden_cross_simple(data)
    
    if golden_crosses:
        print(f"\n发现 {len(golden_crosses)} 个金叉信号：")
        print("-" * 80)
        
        for i, cross in enumerate(golden_crosses, 1):
            print(f"{i:2d}. 日期: {cross['date'].strftime('%Y-%m-%d')} | "
                  f"收盘价: ${cross['price']:8.2f} | "
                  f"MA5: ${cross['ma5']:8.2f} | "
                  f"MA10: ${cross['ma10']:8.2f}")
        
        # 统计信息
        prices = [cross['price'] for cross in golden_crosses]
        print(f"\n统计信息:")
        print(f"- 金叉总数: {len(golden_crosses)}")
        print(f"- 首次金叉: {golden_crosses[0]['date'].strftime('%Y-%m-%d')}")
        print(f"- 最后金叉: {golden_crosses[-1]['date'].strftime('%Y-%m-%d')}")
        print(f"- 金叉时平均价格: ${sum(prices)/len(prices):.2f}")
        print(f"- 金叉时最高价格: ${max(prices):.2f}")
        print(f"- 金叉时最低价格: ${min(prices):.2f}")
        
        # 按年份统计
        yearly_count = {}
        for cross in golden_crosses:
            year = cross['date'].year
            yearly_count[year] = yearly_count.get(year, 0) + 1
        
        print(f"\n按年份统计:")
        for year in sorted(yearly_count.keys()):
            print(f"- {year}年: {yearly_count[year]}次金叉")
    else:
        print("未发现金叉信号")
    
    return golden_crosses

def main():
    """主函数"""
    print("股票金叉分析 - 简化版演示")
    print("=" * 50)
    print("使用滑动窗口技术检测5日线上穿10日线的金叉信号")
    
    # 分析三个股票
    apple_crosses = analyze_stock_simple('Apple Dataset.csv', 'Apple (AAPL)')
    google_crosses = analyze_stock_simple('GOOGL.csv', 'Google (GOOGL)')
    tesla_crosses = analyze_stock_simple('Tesla.csv', 'Tesla (TSLA)')
    
    # 综合统计
    print(f"\n========================================")
    print(f"综合分析：三个股票金叉对比")
    print(f"========================================")
    
    total_stats = {
        'AAPL': len(apple_crosses),
        'GOOGL': len(google_crosses),
        'TSLA': len(tesla_crosses)
    }
    
    print("金叉次数统计:")
    for stock, count in total_stats.items():
        print(f"- {stock}: {count}次")
    
    total_crosses = sum(total_stats.values())
    print(f"- 总计: {total_crosses}次")
    
    if total_crosses > 0:
        max_stock = max(total_stats, key=total_stats.get)
        print(f"\n金叉次数最多的股票: {max_stock} ({total_stats[max_stock]}次)")
    
    print(f"\n分析完成！")
    print("\n技术说明:")
    print("- 使用滑动窗口计算5日和10日移动平均线")
    print("- 金叉定义：前一日5日线≤10日线，当日5日线>10日线")
    print("- 需要至少10个交易日的数据才开始检测金叉")

if __name__ == "__main__":
    main()
